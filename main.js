// Main JavaScript for the Monthsary Website

// Initialize particles.js
function initParticles() {
    particlesJS('particles-js', {
        particles: {
            number: {
                value: 50,
                density: {
                    enable: true,
                    value_area: 800
                }
            },
            color: {
                value: ['#ff6b9d', '#ffc3d8', '#c44569']
            },
            shape: {
                type: 'circle',
                stroke: {
                    width: 0,
                    color: '#000000'
                }
            },
            opacity: {
                value: 0.6,
                random: true,
                anim: {
                    enable: true,
                    speed: 1,
                    opacity_min: 0.1,
                    sync: false
                }
            },
            size: {
                value: 3,
                random: true,
                anim: {
                    enable: true,
                    speed: 2,
                    size_min: 0.1,
                    sync: false
                }
            },
            line_linked: {
                enable: false
            },
            move: {
                enable: true,
                speed: 1,
                direction: 'top',
                random: true,
                straight: false,
                out_mode: 'out',
                bounce: false
            }
        },
        interactivity: {
            detect_on: 'canvas',
            events: {
                onhover: {
                    enable: true,
                    mode: 'bubble'
                },
                onclick: {
                    enable: true,
                    mode: 'push'
                },
                resize: true
            },
            modes: {
                bubble: {
                    distance: 100,
                    size: 6,
                    duration: 2,
                    opacity: 0.8,
                    speed: 3
                },
                push: {
                    particles_nb: 4
                }
            }
        },
        retina_detect: true
    });
}

// Loading screen animation
function hideLoadingScreen() {
    setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        const mainContent = document.getElementById('main-content');
        
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
            mainContent.style.opacity = '1';
        }, 500);
    }, 2000);
}

// Theme toggle functionality
function initThemeToggle() {
    const themeBtn = document.getElementById('theme-btn');
    const body = document.body;
    
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        body.setAttribute('data-theme', savedTheme);
    }
    
    themeBtn.addEventListener('click', () => {
        const currentTheme = body.getAttribute('data-theme');
        const newTheme = currentTheme === 'night' ? 'day' : 'night';
        
        body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        // Update particles color for theme
        updateParticlesTheme(newTheme);
    });
}

// Update particles theme
function updateParticlesTheme(theme) {
    const colors = theme === 'night' 
        ? ['#ff8fab', '#d4a5c7', '#8e44ad']
        : ['#ff6b9d', '#ffc3d8', '#c44569'];
    
    if (window.pJSDom && window.pJSDom[0]) {
        window.pJSDom[0].pJS.particles.color.value = colors;
    }
}

// Code animation in terminal
function initCodeAnimation() {
    const runButton = document.getElementById('run-code');
    const codeContent = document.getElementById('code-content');
    const outputContent = document.getElementById('output-content');
    
    runButton.addEventListener('click', () => {
        runButton.disabled = true;
        runButton.textContent = 'Running... ⏳';
        
        // Hide code and show output with typing effect
        setTimeout(() => {
            codeContent.style.display = 'none';
            outputContent.style.display = 'block';
            
            const outputLines = outputContent.querySelectorAll('.output-line');
            outputLines.forEach((line, index) => {
                line.style.opacity = '0';
                setTimeout(() => {
                    line.style.opacity = '1';
                    line.style.animation = 'typewriter 0.5s ease-in-out';
                }, index * 300);
            });
            
            runButton.textContent = 'Code Executed! ✅';
        }, 1000);
    });
}

// Countdown timer
function initCountdown() {
    // Set the date for 2nd monthsary (30 days from now as example)
    const now = new Date();
    const secondMonthsary = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
    
    function updateCountdown() {
        const now = new Date().getTime();
        const distance = secondMonthsary - now;
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        document.getElementById('days').textContent = days.toString().padStart(2, '0');
        document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
        document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
        document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        
        if (distance < 0) {
            document.querySelector('.countdown-display').innerHTML = 
                '<h3 style="color: var(--primary-pink);">🎉 It\'s our 2nd Monthsary! 🎉</h3>';
        }
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
}

// Secret heart easter egg
function initEasterEgg() {
    const secretHeart = document.getElementById('secret-heart');
    const modal = document.getElementById('secret-modal');
    const closeBtn = document.querySelector('.close');
    const fireworksBtn = document.getElementById('fireworks-btn');
    
    secretHeart.addEventListener('click', () => {
        modal.style.display = 'block';
        // Add sparkle effect
        createSparkles(secretHeart);
    });
    
    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });
    
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    fireworksBtn.addEventListener('click', () => {
        launchFireworks();
        modal.style.display = 'none';
    });
}

// Create sparkle effect
function createSparkles(element) {
    for (let i = 0; i < 10; i++) {
        const sparkle = document.createElement('div');
        sparkle.innerHTML = '✨';
        sparkle.style.position = 'absolute';
        sparkle.style.fontSize = '1rem';
        sparkle.style.pointerEvents = 'none';
        sparkle.style.zIndex = '9999';
        
        const rect = element.getBoundingClientRect();
        sparkle.style.left = (rect.left + Math.random() * rect.width) + 'px';
        sparkle.style.top = (rect.top + Math.random() * rect.height) + 'px';
        
        document.body.appendChild(sparkle);
        
        // Animate sparkle
        sparkle.animate([
            { transform: 'translateY(0px) scale(0)', opacity: 1 },
            { transform: 'translateY(-50px) scale(1)', opacity: 0 }
        ], {
            duration: 1000,
            easing: 'ease-out'
        }).onfinish = () => sparkle.remove();
    }
}

// Fireworks animation
function launchFireworks() {
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };
    
    function randomInRange(min, max) {
        return Math.random() * (max - min) + min;
    }
    
    const interval = setInterval(() => {
        const timeLeft = animationEnd - Date.now();
        
        if (timeLeft <= 0) {
            return clearInterval(interval);
        }
        
        const particleCount = 50 * (timeLeft / duration);
        
        // Launch from different positions
        confetti(Object.assign({}, defaults, {
            particleCount,
            origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
            colors: ['#ff6b9d', '#ffc3d8', '#c44569', '#ff8fab']
        }));
        
        confetti(Object.assign({}, defaults, {
            particleCount,
            origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
            colors: ['#ff6b9d', '#ffc3d8', '#c44569', '#ff8fab']
        }));
    }, 250);
}

// PDF Download functionality
function initPDFDownload() {
    const downloadBtn = document.getElementById('download-pdf');
    
    downloadBtn.addEventListener('click', () => {
        // Create a simple text version for download
        const loveLetterText = `
💖 Happy 1st Monthsary, My Love 💖

"It's only been a month, but it already feels like forever — in the best way."

Our Beautiful Beginning:
👋 First Hello - The moment our paths crossed and everything changed
💬 First Conversation - Hours flew by like minutes as we talked and laughed  
📱 First Video Call - Seeing your smile light up my screen for the first time
💕 First "I Love You" - The words that made everything official and perfect

My Favorite Things About You:
😊 Your Smile - It lights up my entire world and makes everything better
😂 Your Laugh - The most beautiful sound that never fails to make me happy
🤗 Your Hugs - Even through the screen, they feel like home
😴 Your Sleepy Voice - So soft and sweet, it melts my heart every time
🔥 Your Passion - The way you light up when talking about things you love
💖 Your Heart - So kind, caring, and beautiful in every way

A Love Letter from a Coder:
"My dearest love, in just one month, you've become my everything.
Every line of code I write, every bug I fix, reminds me that the most 
beautiful thing I've ever created is this love we share. You're not just 
my girlfriend, you're my favorite feature, my best function, and the 
perfect solution to my heart's algorithm. I love you beyond syntax 
errors and infinite loops. Forever yours, Your Coding Boyfriend 💖"

My Promises to You:
👂 I promise to always listen to you with my whole heart
💪 I promise to never stop trying to be better for you
🤝 I promise to always show up when you need me
😊 I promise to make you smile every single day
🌟 I promise to support your dreams and celebrate your wins
💖 I promise to love you more with each passing day

"This is just Month 1 — wait 'til you see what Month 2 brings. 
I have so many more surprises, adventures, and love to share with you. 
Thank you for being the most amazing girlfriend ever. 
Here's to forever and always! 💖✨"

With all my love,
Your Boyfriend 💕
        `;
        
        const blob = new Blob([loveLetterText], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'Love_Letter_1st_Monthsary.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        // Show success message
        downloadBtn.textContent = 'Downloaded! 💖';
        setTimeout(() => {
            downloadBtn.textContent = 'Download Love Letter PDF 📄💖';
        }, 2000);
    });
}

// Smooth scroll for navigation
function initSmoothScroll() {
    // Add scroll animations for elements
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.8s ease-out forwards';
            }
        });
    }, observerOptions);
    
    // Observe all sections
    document.querySelectorAll('.section').forEach(section => {
        observer.observe(section);
    });
}

// Add fadeInUp animation to CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initParticles();
    hideLoadingScreen();
    initThemeToggle();
    initCodeAnimation();
    initCountdown();
    initEasterEgg();
    initPDFDownload();
    initSmoothScroll();
    
    console.log('💖 Welcome to our love story! 💖');
    console.log('This website was made with love for the most amazing girlfriend ever! 💕');
});

// Add some romantic console messages
setTimeout(() => {
    console.log('💕 I love you more than all the stars in the sky! 💕');
}, 3000);

setTimeout(() => {
    console.log('🌸 Thank you for being the best part of my life! 🌸');
}, 6000);

setTimeout(() => {
    console.log('💖 Here\'s to many more months together! 💖');
}, 9000);
