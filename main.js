// Main JavaScript for the Monthsary Website

// Create floating hearts animation
function createFloatingHearts() {
    const heartsContainer = document.createElement('div');
    heartsContainer.className = 'floating-hearts-bg';
    heartsContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
    `;
    document.body.appendChild(heartsContainer);

    function createHeart() {
        const heart = document.createElement('div');
        const hearts = ['💕', '💖', '💗', '💝', '💘', '✨'];
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.cssText = `
            position: absolute;
            font-size: ${Math.random() * 20 + 10}px;
            left: ${Math.random() * 100}%;
            top: 100%;
            opacity: ${Math.random() * 0.5 + 0.3};
            animation: floatUp ${Math.random() * 10 + 10}s linear infinite;
        `;
        heartsContainer.appendChild(heart);

        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 20000);
    }

    // Create hearts periodically
    setInterval(createHeart, 2000);

    // Add CSS for float animation
    if (!document.querySelector('#float-animation')) {
        const style = document.createElement('style');
        style.id = 'float-animation';
        style.textContent = `
            @keyframes floatUp {
                0% {
                    transform: translateY(0) rotate(0deg);
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Loading screen animation
function hideLoadingScreen() {
    setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        const mainContent = document.getElementById('main-content');

        if (loadingScreen && mainContent) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                mainContent.style.opacity = '1';
            }, 500);
        }
    }, 3000); // Increased to 3 seconds for better effect
}

// Theme toggle functionality
function initThemeToggle() {
    const themeBtn = document.getElementById('theme-btn');
    const body = document.body;

    if (!themeBtn) return;

    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        body.setAttribute('data-theme', savedTheme);
    }

    themeBtn.addEventListener('click', () => {
        const currentTheme = body.getAttribute('data-theme');
        const newTheme = currentTheme === 'night' ? 'day' : 'night';

        body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
    });
}

// Code animation in terminal
function initCodeAnimation() {
    const runButton = document.getElementById('run-code');
    const codeContent = document.getElementById('code-content');
    const outputContent = document.getElementById('output-content');
    
    runButton.addEventListener('click', () => {
        runButton.disabled = true;
        runButton.textContent = 'Running... ⏳';
        
        // Hide code and show output with typing effect
        setTimeout(() => {
            codeContent.style.display = 'none';
            outputContent.style.display = 'block';
            
            const outputLines = outputContent.querySelectorAll('.output-line');
            outputLines.forEach((line, index) => {
                line.style.opacity = '0';
                setTimeout(() => {
                    line.style.opacity = '1';
                    line.style.animation = 'typewriter 0.5s ease-in-out';
                }, index * 300);
            });
            
            runButton.textContent = 'Code Executed! ✅';
        }, 1000);
    });
}

// Countdown timer
function initCountdown() {
    // Set the date for 2nd monthsary (30 days from now as example)
    const now = new Date();
    const secondMonthsary = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
    
    function updateCountdown() {
        const now = new Date().getTime();
        const distance = secondMonthsary - now;
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        document.getElementById('days').textContent = days.toString().padStart(2, '0');
        document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
        document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
        document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        
        if (distance < 0) {
            document.querySelector('.countdown-display').innerHTML = 
                '<h3 style="color: var(--primary-pink);">🎉 It\'s our 2nd Monthsary! 🎉</h3>';
        }
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
}

// Secret heart easter egg
function initEasterEgg() {
    const secretHeart = document.getElementById('secret-heart');
    const modal = document.getElementById('secret-modal');
    const closeBtn = document.querySelector('.close');
    const fireworksBtn = document.getElementById('fireworks-btn');
    
    secretHeart.addEventListener('click', () => {
        modal.style.display = 'block';
        // Add sparkle effect
        createSparkles(secretHeart);
    });
    
    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });
    
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    fireworksBtn.addEventListener('click', () => {
        launchFireworks();
        modal.style.display = 'none';
    });
}

// Create sparkle effect
function createSparkles(element) {
    for (let i = 0; i < 10; i++) {
        const sparkle = document.createElement('div');
        sparkle.innerHTML = '✨';
        sparkle.style.position = 'absolute';
        sparkle.style.fontSize = '1rem';
        sparkle.style.pointerEvents = 'none';
        sparkle.style.zIndex = '9999';
        
        const rect = element.getBoundingClientRect();
        sparkle.style.left = (rect.left + Math.random() * rect.width) + 'px';
        sparkle.style.top = (rect.top + Math.random() * rect.height) + 'px';
        
        document.body.appendChild(sparkle);
        
        // Animate sparkle
        sparkle.animate([
            { transform: 'translateY(0px) scale(0)', opacity: 1 },
            { transform: 'translateY(-50px) scale(1)', opacity: 0 }
        ], {
            duration: 1000,
            easing: 'ease-out'
        }).onfinish = () => sparkle.remove();
    }
}

// Simple fireworks animation without external library
function launchFireworks() {
    const colors = ['#ff6b9d', '#ffc3d8', '#c44569', '#ff8fab'];

    for (let i = 0; i < 50; i++) {
        setTimeout(() => {
            createFireworkParticle(colors[Math.floor(Math.random() * colors.length)]);
        }, i * 100);
    }
}

function createFireworkParticle(color) {
    const particle = document.createElement('div');
    particle.style.cssText = `
        position: fixed;
        width: 6px;
        height: 6px;
        background: ${color};
        border-radius: 50%;
        pointer-events: none;
        z-index: 10000;
        left: ${Math.random() * window.innerWidth}px;
        top: ${Math.random() * window.innerHeight}px;
    `;

    document.body.appendChild(particle);

    // Animate the particle
    particle.animate([
        { transform: 'scale(0) translate(0, 0)', opacity: 1 },
        { transform: `scale(1) translate(${(Math.random() - 0.5) * 200}px, ${(Math.random() - 0.5) * 200}px)`, opacity: 0 }
    ], {
        duration: 1000,
        easing: 'ease-out'
    }).onfinish = () => particle.remove();
}

// PDF Download functionality
function initPDFDownload() {
    const downloadBtn = document.getElementById('download-pdf');
    
    downloadBtn.addEventListener('click', () => {
        // Create a simple text version for download
        const loveLetterText = `
💖 Happy 1st Monthsary, My Love 💖

"It's only been a month, but it already feels like forever — in the best way."

Our Beautiful Beginning:
👋 First Hello - The moment our paths crossed and everything changed
💬 First Conversation - Hours flew by like minutes as we talked and laughed  
📱 First Video Call - Seeing your smile light up my screen for the first time
💕 First "I Love You" - The words that made everything official and perfect

My Favorite Things About You:
😊 Your Smile - It lights up my entire world and makes everything better
😂 Your Laugh - The most beautiful sound that never fails to make me happy
🤗 Your Hugs - Even through the screen, they feel like home
😴 Your Sleepy Voice - So soft and sweet, it melts my heart every time
🔥 Your Passion - The way you light up when talking about things you love
💖 Your Heart - So kind, caring, and beautiful in every way

A Love Letter from a Coder:
"My dearest love, in just one month, you've become my everything.
Every line of code I write, every bug I fix, reminds me that the most 
beautiful thing I've ever created is this love we share. You're not just 
my girlfriend, you're my favorite feature, my best function, and the 
perfect solution to my heart's algorithm. I love you beyond syntax 
errors and infinite loops. Forever yours, Your Coding Boyfriend 💖"

My Promises to You:
👂 I promise to always listen to you with my whole heart
💪 I promise to never stop trying to be better for you
🤝 I promise to always show up when you need me
😊 I promise to make you smile every single day
🌟 I promise to support your dreams and celebrate your wins
💖 I promise to love you more with each passing day

"This is just Month 1 — wait 'til you see what Month 2 brings. 
I have so many more surprises, adventures, and love to share with you. 
Thank you for being the most amazing girlfriend ever. 
Here's to forever and always! 💖✨"

With all my love,
Your Boyfriend 💕
        `;
        
        const blob = new Blob([loveLetterText], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'Love_Letter_1st_Monthsary.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        // Show success message
        downloadBtn.textContent = 'Downloaded! 💖';
        setTimeout(() => {
            downloadBtn.textContent = 'Download Love Letter PDF 📄💖';
        }, 2000);
    });
}

// Smooth scroll for navigation
function initSmoothScroll() {
    // Add scroll animations for elements
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.8s ease-out forwards';
            }
        });
    }, observerOptions);
    
    // Observe all sections
    document.querySelectorAll('.section').forEach(section => {
        observer.observe(section);
    });
}

// Add fadeInUp animation to CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    createFloatingHearts();
    hideLoadingScreen();
    initThemeToggle();
    initCodeAnimation();
    initCountdown();
    initEasterEgg();
    initPDFDownload();
    initSmoothScroll();

    console.log('💖 Welcome to our love story! 💖');
    console.log('This website was made with love for the most amazing girlfriend ever! 💕');
});

// Add some romantic console messages
setTimeout(() => {
    console.log('💕 I love you more than all the stars in the sky! 💕');
}, 3000);

setTimeout(() => {
    console.log('🌸 Thank you for being the best part of my life! 🌸');
}, 6000);

setTimeout(() => {
    console.log('💖 Here\'s to many more months together! 💖');
}, 9000);
