/* CSS Variables for Theme */
:root {
  --primary-pink: #ff6b9d;
  --secondary-pink: #ffc3d8;
  --light-pink: #fff0f5;
  --accent-purple: #c44569;
  --soft-white: #fefefe;
  --text-dark: #2c2c2c;
  --text-light: #666;
  --gradient-bg: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  --gradient-card: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 195, 216, 0.3));
  --shadow-soft: 0 10px 30px rgba(255, 107, 157, 0.2);
  --shadow-hover: 0 15px 40px rgba(255, 107, 157, 0.3);
}

[data-theme="night"] {
  --primary-pink: #ff8fab;
  --secondary-pink: #d4a5c7;
  --light-pink: #2a1f3d;
  --accent-purple: #8e44ad;
  --soft-white: #1a1a2e;
  --text-dark: #f8f8f8;
  --text-light: #cccccc;
  --gradient-bg: linear-gradient(135deg, #2c1810 0%, #3d2f47 50%, #4a3c5a 100%);
  --gradient-card: linear-gradient(145deg, rgba(42, 31, 61, 0.9), rgba(212, 165, 199, 0.1));
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Poppins', sans-serif;
  background: var(--gradient-bg);
  color: var(--text-dark);
  overflow-x: hidden;
  min-height: 100vh;
  transition: all 0.5s ease;
}

/* Animated Background */
#animated-bg {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
}

#animated-bg::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 107, 157, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 195, 216, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(196, 69, 105, 0.2) 0%, transparent 50%);
  animation: float-bg 20s ease-in-out infinite;
}

#animated-bg::after {
  content: '💕 💖 💗 💝 💘 ✨ 🌸 💫 💕 💖 💗 💝 💘 ✨ 🌸 💫';
  position: absolute;
  width: 100%;
  height: 100%;
  font-size: 2rem;
  opacity: 0.1;
  animation: sparkle-float 15s linear infinite;
  white-space: nowrap;
  line-height: 4rem;
}

@keyframes float-bg {
  0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
  33% { transform: translate(-48%, -52%) rotate(1deg); }
  66% { transform: translate(-52%, -48%) rotate(-1deg); }
}

@keyframes sparkle-float {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Loading Screen */
#loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease;
}

.loading-content {
  text-align: center;
}

.heart-loader {
  font-size: 4rem;
  animation: heartbeat 1.5s ease-in-out infinite;
  margin-bottom: 1rem;
}

.loading-text {
  font-family: 'Dancing Script', cursive;
  font-size: 1.5rem;
  color: var(--accent-purple);
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* Theme Toggle */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.theme-button {
  background: var(--gradient-card);
  border: none;
  border-radius: 50px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 1.2rem;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.theme-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.theme-button .moon {
  display: none;
}

[data-theme="night"] .theme-button .sun {
  display: none;
}

[data-theme="night"] .theme-button .moon {
  display: inline;
}

/* Main Content */
#main-content {
  transition: opacity 1s ease;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section {
  padding: 80px 0;
  position: relative;
}

.section-title {
  font-family: 'Playfair Display', serif;
  font-size: 3rem;
  text-align: center;
  margin-bottom: 3rem;
  color: var(--accent-purple);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: var(--primary-pink);
  border-radius: 2px;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-content {
  z-index: 2;
  position: relative;
  max-width: 800px;
  padding: 2rem;
}

.hero-decoration {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.sparkle {
  position: absolute;
  font-size: 2rem;
  animation: sparkleFloat 8s ease-in-out infinite;
}

.sparkle-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.sparkle-2 {
  top: 30%;
  right: 20%;
  animation-delay: 2s;
}

.sparkle-3 {
  bottom: 30%;
  left: 10%;
  animation-delay: 4s;
}

.sparkle-4 {
  bottom: 20%;
  right: 15%;
  animation-delay: 6s;
}

@keyframes sparkleFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) scale(0.8);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.9;
  }
}

.main-title {
  font-family: 'Dancing Script', cursive;
  font-size: 4.5rem;
  font-weight: 700;
  color: var(--accent-purple);
  margin-bottom: 2rem;
  line-height: 1.2;
}

.title-line {
  display: block;
  animation: slideInUp 1s ease-out forwards;
  opacity: 0;
}

.title-line:nth-child(2) {
  animation-delay: 0.5s;
}

.hero-quote {
  font-family: 'Playfair Display', serif;
  font-style: italic;
  font-size: 1.5rem;
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto 2rem;
  animation: fadeIn 1s ease-out 1s forwards;
  opacity: 0;
  line-height: 1.6;
}

.hero-date {
  margin: 2rem 0 3rem;
  animation: fadeIn 1s ease-out 1.5s forwards;
  opacity: 0;
}

.date-highlight {
  display: block;
  font-family: 'Dancing Script', cursive;
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--primary-pink);
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(255, 107, 157, 0.3);
}

.date-subtitle {
  font-family: 'Playfair Display', serif;
  font-style: italic;
  font-size: 1.2rem;
  color: var(--accent-purple);
}

.floating-hearts {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.floating-hearts .heart {
  position: absolute;
  font-size: 2rem;
  animation: float 6s ease-in-out infinite;
  opacity: 0.7;
}

.floating-hearts .heart:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
}

.floating-hearts .heart:nth-child(2) {
  left: 20%;
  animation-delay: 1s;
}

.floating-hearts .heart:nth-child(3) {
  left: 70%;
  animation-delay: 2s;
}

.floating-hearts .heart:nth-child(4) {
  left: 80%;
  animation-delay: 3s;
}

.floating-hearts .heart:nth-child(5) {
  left: 90%;
  animation-delay: 4s;
}

.floating-hearts .heart:nth-child(6) {
  left: 30%;
  animation-delay: 5s;
}

.floating-hearts .heart:nth-child(7) {
  left: 60%;
  animation-delay: 6s;
}

.floating-hearts .heart:nth-child(8) {
  left: 50%;
  animation-delay: 7s;
}

@keyframes slideInUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(5deg);
  }
  50% {
    transform: translateY(-40px) rotate(-5deg);
  }
  75% {
    transform: translateY(-20px) rotate(3deg);
  }
}

/* Timeline Section */
.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 100%;
  background: var(--primary-pink);
  border-radius: 2px;
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-card);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  box-shadow: var(--shadow-soft);
  z-index: 2;
  position: relative;
}

.timeline-content {
  flex: 1;
  background: var(--gradient-card);
  padding: 2rem;
  border-radius: 20px;
  margin: 0 2rem;
  box-shadow: var(--shadow-soft);
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}

.timeline-content:hover {
  transform: translateY(-5px);
}

.timeline-content h3 {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  color: var(--accent-purple);
  margin-bottom: 0.5rem;
}

/* Favorites Grid */
.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.favorite-card {
  background: var(--gradient-card);
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.favorite-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-hover);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.favorite-card h3 {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  color: var(--accent-purple);
  margin-bottom: 1rem;
}

/* Photo Gallery */
.photo-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.polaroid {
  background: var(--soft-white);
  padding: 1rem;
  border-radius: 10px;
  box-shadow: var(--shadow-soft);
  transform: rotate(-2deg);
  transition: all 0.3s ease;
}

.polaroid:nth-child(even) {
  transform: rotate(2deg);
}

.polaroid:hover {
  transform: rotate(0deg) scale(1.05);
  box-shadow: var(--shadow-hover);
}

.photo-placeholder {
  background: var(--light-pink);
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  margin-bottom: 1rem;
}

.photo-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.polaroid-caption {
  font-family: 'Dancing Script', cursive;
  font-size: 1.2rem;
  text-align: center;
  color: var(--text-dark);
}

/* Terminal/Code Section */
.terminal {
  background: #1e1e1e;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow-soft);
  margin: 2rem 0;
}

.terminal-header {
  background: #333;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.terminal-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.btn.red { background: #ff5f56; }
.btn.yellow { background: #ffbd2e; }
.btn.green { background: #27ca3f; }

.terminal-title {
  color: #fff;
  font-family: 'Courier New', monospace;
}

.terminal-body {
  padding: 2rem;
  background: #1e1e1e;
  color: #fff;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  min-height: 300px;
}

.code-line, .output-line {
  margin-bottom: 0.5rem;
  animation: typewriter 0.5s ease-in-out;
}

.keyword { color: #569cd6; }
.variable { color: #9cdcfe; }
.string { color: #ce9178; }
.number { color: #b5cea8; }
.function { color: #dcdcaa; }
.comment { color: #6a9955; }

.run-button {
  background: var(--primary-pink);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.run-button:hover {
  background: var(--accent-purple);
  transform: translateY(-2px);
}

@keyframes typewriter {
  from { opacity: 0; transform: translateX(-10px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Music Section */
.music-player {
  background: var(--gradient-card);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: var(--shadow-soft);
  backdrop-filter: blur(10px);
  text-align: center;
}

.music-info h3 {
  font-family: 'Playfair Display', serif;
  font-size: 1.8rem;
  color: var(--accent-purple);
  margin-bottom: 1rem;
}

.spotify-embed {
  margin-top: 2rem;
}

/* Promises Section */
.promises-list {
  display: grid;
  gap: 1.5rem;
  margin-top: 2rem;
}

.promise-item {
  background: var(--gradient-card);
  padding: 1.5rem;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.promise-item:hover {
  transform: translateX(10px);
  box-shadow: var(--shadow-hover);
}

.promise-icon {
  font-size: 2rem;
  min-width: 60px;
  text-align: center;
}

.promise-item p {
  font-size: 1.1rem;
  color: var(--text-dark);
}

/* Time Together Section */
.time-together {
  background: linear-gradient(rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5)),
              url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><text x="50" y="50" font-size="20" text-anchor="middle" dominant-baseline="middle" fill="%23ffc3d8">💖</text></svg>');
  background-size: cover;
  position: relative;
  padding: 100px 0;
}

.time-together::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-bg);
  opacity: 0.85;
  z-index: -1;
}

.love-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: var(--gradient-card);
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: var(--shadow-soft);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.stat-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
  border-color: var(--primary-pink);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: bold;
  color: var(--accent-purple);
  font-family: 'Playfair Display', serif;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(255, 107, 157, 0.3);
}

.stat-label {
  font-size: 1rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.milestone-message {
  background: var(--gradient-card);
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  box-shadow: var(--shadow-soft);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.milestone-text {
  font-family: 'Dancing Script', cursive;
  font-size: 1.8rem;
  color: var(--accent-purple);
  line-height: 1.6;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Floating Surprise */
.floating-surprise {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.secret-heart {
  font-size: 3rem;
  cursor: pointer;
  animation: bounce 2s infinite;
  transition: all 0.3s ease;
}

.secret-heart:hover {
  transform: scale(1.2);
  filter: drop-shadow(0 0 20px var(--primary-pink));
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--gradient-card);
  margin: 10% auto;
  padding: 2rem;
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  text-align: center;
  box-shadow: var(--shadow-hover);
  backdrop-filter: blur(10px);
  position: relative;
}

.close {
  position: absolute;
  right: 1rem;
  top: 1rem;
  font-size: 2rem;
  cursor: pointer;
  color: var(--text-light);
}

.secret-message {
  font-family: 'Playfair Display', serif;
  font-style: italic;
  font-size: 1.2rem;
  line-height: 1.6;
  margin: 2rem 0;
  color: var(--text-dark);
}

.surprise-button {
  background: var(--primary-pink);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.surprise-button:hover {
  background: var(--accent-purple);
  transform: scale(1.05);
}

/* Download Section */
.download-button {
  background: var(--gradient-card);
  color: var(--accent-purple);
  border: 2px solid var(--primary-pink);
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.download-button:hover {
  background: var(--primary-pink);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-title {
    font-size: 3rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .hero-quote {
    font-size: 1.2rem;
  }

  .date-highlight {
    font-size: 2rem;
  }

  .sparkle {
    font-size: 1.5rem;
  }

  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    padding-left: 80px;
  }

  .timeline-icon {
    position: absolute;
    left: 0;
  }

  .timeline-content {
    margin-left: 1rem;
    margin-right: 0;
  }

  .favorites-grid {
    grid-template-columns: 1fr;
  }

  .love-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .milestone-text {
    font-size: 1.5rem;
  }

  .floating-surprise {
    bottom: 20px;
    right: 20px;
  }

  .secret-heart {
    font-size: 2.5rem;
  }

  .hero-content {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .love-stats {
    grid-template-columns: 1fr;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }
}
